#!/usr/bin/env elixir

# Example: CreateUser Operation with Telemetry
#
# This example demonstrates:
# 1. Setting up a CreateUser form command operation
# 2. Using Ecto schema for database operations
# 3. Enabling telemetry extension for monitoring
# 4. Setting up telemetry handlers to log operation events
#
# To run this example:
# $ elixir examples/operations/create_user_with_telemetry.ex

# Add the project root to the code path
Code.prepend_path("lib")

# Load dependencies
Mix.install([
  {:drops, path: "."},
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"}
])

# Configure the repository for this example
Application.put_env(:example_app, ExampleApp.Repo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool_size: 1,
  log: false
)

# Define the repository
defmodule ExampleApp.Repo do
  use Ecto.Repo,
    otp_app: :example_app,
    adapter: Ecto.Adapters.SQLite3
end

# Define the User schema
defmodule ExampleApp.User do
  use Ecto.Schema
  import Ecto.Changeset

  schema "users" do
    field(:name, :string)
    field(:email, :string)
    field(:age, :integer)

    timestamps()
  end

  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email, :age])
    |> validate_required([:name, :email])
    |> validate_format(:email, ~r/@/)
    |> validate_number(:age, greater_than: 0, less_than: 150)
    |> unique_constraint(:email)
  end
end

# Define telemetry handlers
defmodule ExampleApp.TelemetryHandler do
  require Logger

  def setup_handlers do
    :telemetry.attach_many(
      "example-operations-telemetry",
      [
        [:drops, :operations, :operation, :start],
        [:drops, :operations, :operation, :stop],
        [:drops, :operations, :operation, :exception],
        [:drops, :operations, :step, :start],
        [:drops, :operations, :step, :stop],
        [:drops, :operations, :step, :exception]
      ],
      &__MODULE__.handle_event/4,
      %{}
    )
  end

  def handle_event(
        [:drops, :operations, :operation, :start],
        measurements,
        metadata,
        _config
      ) do
    IO.puts(
      "🚀 Starting operation #{inspect(metadata.operation)} with step #{metadata.step}"
    )

    IO.puts("   Context: #{inspect(metadata.context)}")
  end

  def handle_event(
        [:drops, :operations, :operation, :stop],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
    IO.puts("✅ Completed operation #{inspect(metadata.operation)} in #{duration_ms}ms")
  end

  def handle_event(
        [:drops, :operations, :operation, :exception],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
    IO.puts("❌ Failed operation #{inspect(metadata.operation)} after #{duration_ms}ms")
    IO.puts("   Error: #{inspect(metadata.reason)}")
  end

  def handle_event([:drops, :operations, :step, :start], measurements, metadata, _config) do
    IO.puts("  🔄 Starting step #{metadata.step} in #{inspect(metadata.operation)}")
  end

  def handle_event([:drops, :operations, :step, :stop], measurements, metadata, _config) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
    IO.puts("  ✅ Completed step #{metadata.step} in #{duration_ms}ms")
  end

  def handle_event(
        [:drops, :operations, :step, :exception],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
    IO.puts("  ❌ Failed step #{metadata.step} after #{duration_ms}ms")
    IO.puts("     Error: #{inspect(metadata.reason)}")
  end
end

# Define the CreateUser operation with telemetry enabled
defmodule ExampleApp.CreateUser do
  use Drops.Operations.Command, telemetry: true

  # Use Ecto schema inference for automatic validation
  schema(ExampleApp.User)

  steps do
    # Override prepare to add database-specific logic
    def prepare(%{params: params} = context) do
      # Remove any virtual fields and prepare for database insertion
      clean_params = Map.take(params, [:name, :email, :age])
      {:ok, Map.put(context, :params, clean_params)}
    end

    @impl true
    def execute(%{params: params}) do
      # Create changeset and insert into database
      changeset = ExampleApp.User.changeset(%ExampleApp.User{}, params)

      case ExampleApp.Repo.insert(changeset) do
        {:ok, user} ->
          {:ok, user}

        {:error, changeset} ->
          # Convert Ecto changeset errors to a readable format
          errors =
            Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
              Enum.reduce(opts, msg, fn {key, value}, acc ->
                String.replace(acc, "%{#{key}}", to_string(value))
              end)
            end)

          {:error, errors}
      end
    end
  end
end

# Alternative operation with step-level telemetry
defmodule ExampleApp.CreateUserWithStepTelemetry do
  use Drops.Operations.Command, telemetry: [steps: [:validate, :execute]]

  schema(ExampleApp.User)

  steps do
    def prepare(%{params: params} = context) do
      clean_params = Map.take(params, [:name, :email, :age])
      {:ok, Map.put(context, :params, clean_params)}
    end

    def validate(%{params: params} = context) do
      # Custom validation step with telemetry
      changeset = ExampleApp.User.changeset(%ExampleApp.User{}, params)

      if changeset.valid? do
        {:ok, context}
      else
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              String.replace(acc, "%{#{key}}", to_string(value))
            end)
          end)

        {:error, errors}
      end
    end

    @impl true
    def execute(%{params: params}) do
      # Simulate some processing time for telemetry demonstration
      Process.sleep(10)

      case ExampleApp.Repo.insert(%ExampleApp.User{} |> ExampleApp.User.changeset(params)) do
        {:ok, user} -> {:ok, user}
        {:error, changeset} -> {:error, "Database insertion failed"}
      end
    end
  end
end

# Setup and run the example
defmodule ExampleApp.Demo do
  def run do
    IO.puts("=== CreateUser Operation with Telemetry Demo ===\n")

    # Start the repository
    {:ok, _} = ExampleApp.Repo.start_link()

    # Create the users table
    create_users_table()

    # Setup telemetry handlers
    ExampleApp.TelemetryHandler.setup_handlers()

    IO.puts("📊 Telemetry handlers attached. Running operations...\n")

    # Demo 1: Successful operation with default telemetry
    IO.puts("--- Demo 1: Successful User Creation (Operation-level telemetry) ---")

    valid_params = %{
      name: "John Doe",
      email: "<EMAIL>",
      age: 30
    }

    case ExampleApp.CreateUser.call(%{params: valid_params}) do
      {:ok, user} ->
        IO.puts("✅ User created successfully: #{user.name} (ID: #{user.id})")

      {:error, errors} ->
        IO.puts("❌ Failed to create user: #{inspect(errors)}")
    end

    IO.puts("")

    # Demo 2: Failed operation (validation error)
    IO.puts("--- Demo 2: Failed User Creation (Validation Error) ---")

    invalid_params = %{
      name: "",
      email: "invalid-email",
      age: -5
    }

    case ExampleApp.CreateUser.call(%{params: invalid_params}) do
      {:ok, user} ->
        IO.puts("✅ User created successfully: #{user.name}")

      {:error, errors} ->
        IO.puts("❌ Validation failed: #{inspect(errors)}")
    end

    IO.puts("")

    # Demo 3: Operation with step-level telemetry
    IO.puts("--- Demo 3: User Creation with Step-level Telemetry ---")

    step_params = %{
      name: "Jane Smith",
      email: "<EMAIL>",
      age: 25
    }

    case ExampleApp.CreateUserWithStepTelemetry.call(%{params: step_params}) do
      {:ok, user} ->
        IO.puts("✅ User created with step telemetry: #{user.name} (ID: #{user.id})")

      {:error, errors} ->
        IO.puts("❌ Failed to create user: #{inspect(errors)}")
    end

    IO.puts("")

    # Demo 4: Database constraint violation
    IO.puts("--- Demo 4: Database Constraint Violation (Duplicate Email) ---")

    duplicate_params = %{
      name: "John Duplicate",
      # Same email as Demo 1
      email: "<EMAIL>",
      age: 35
    }

    case ExampleApp.CreateUser.call(%{params: duplicate_params}) do
      {:ok, user} ->
        IO.puts("✅ User created successfully: #{user.name}")

      {:error, errors} ->
        IO.puts("❌ Database constraint violation: #{inspect(errors)}")
    end

    IO.puts("\n=== Demo Complete ===")
    IO.puts("Check the output above to see telemetry events for each operation step!")
  end

  defp create_users_table do
    # Create the users table for this example
    Ecto.Adapters.SQL.query!(ExampleApp.Repo, """
    CREATE TABLE users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      age INTEGER,
      inserted_at DATETIME,
      updated_at DATETIME
    )
    """)
  end
end

# Run the demo
ExampleApp.Demo.run()
